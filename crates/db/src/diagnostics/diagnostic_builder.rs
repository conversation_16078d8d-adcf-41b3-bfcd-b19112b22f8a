
use std::borrow::Cow;

use auto_lsp::{
    lsp_types::{
        self, CodeAction, CodeActionKind, DiagnosticRelatedInformation, DiagnosticSeverity,
        DiagnosticTag, NumberOrString, TextEdit,
    },
    tree_sitter,
};

use crate::diagnostics::IdeDiagnostic;

#[derive(<PERSON>bu<PERSON>, <PERSON>lone, PartialEq, Eq)]
pub enum RangeKind<'a> {
    TreeSitter(Cow<'a, tree_sitter::Range>),
    Lsp(Cow<'a, lsp_types::Range>),
}

impl RangeKind<'_> {
    pub fn as_lsp(&self) -> lsp_types::Range {
        match self {
            RangeKind::TreeSitter(range) => lsp_types::Range {
                start: lsp_types::Position::new(
                    range.start_point.row as u32,
                    range.start_point.column as u32,
                ),
                end: lsp_types::Position::new(
                    range.end_point.row as u32,
                    range.end_point.column as u32,
                ),
            },
            RangeKind::Lsp(range) => **range,
        }
    }
}

impl From<RangeKind<'_>> for lsp_types::Range {
    fn from(range: RangeKind) -> Self {
        range.as_lsp()
    }
}

impl From<tree_sitter::Range> for RangeKind<'_> {
    fn from(range: tree_sitter::Range) -> Self {
        RangeKind::TreeSitter(Cow::Owned(range))
    }
}

impl From<lsp_types::Range> for RangeKind<'_> {
    fn from(range: lsp_types::Range) -> Self {
        RangeKind::Lsp(Cow::Owned(range))
    }
}

impl<'a> From<&'a tree_sitter::Range> for RangeKind<'a> {
    fn from(range: &'a tree_sitter::Range) -> Self {
        RangeKind::TreeSitter(Cow::Borrowed(range))
    }
}

impl<'a> From<&'a lsp_types::Range> for RangeKind<'a> {
    fn from(range: &'a lsp_types::Range) -> Self {
        RangeKind::Lsp(Cow::Borrowed(range))
    }
}

impl PartialOrd for RangeKind<'_> {
    fn partial_cmp(&self, other: &Self) -> Option<std::cmp::Ordering> {
        Some(self.cmp(other))
    }
}

impl Ord for RangeKind<'_> {
    fn cmp(&self, other: &Self) -> std::cmp::Ordering {
        self.as_lsp().start.cmp(&other.as_lsp().start)
    }
}

impl PartialEq<tree_sitter::Range> for RangeKind<'_> {
    fn eq(&self, other: &tree_sitter::Range) -> bool {
        self.as_lsp().start.character == other.start_point.column as u32
            && self.as_lsp().start.line == other.start_point.row as u32
            && self.as_lsp().end.character == other.end_point.column as u32
            && self.as_lsp().end.line == other.end_point.row as u32
    }
}

impl PartialOrd<tree_sitter::Range> for RangeKind<'_> {
    fn partial_cmp(&self, other: &tree_sitter::Range) -> Option<std::cmp::Ordering> {
        Some(self.cmp(&other.into()))
    }
}

impl PartialEq<lsp_types::Range> for RangeKind<'_> {
    fn eq(&self, other: &lsp_types::Range) -> bool {
        self.as_lsp().start.character == other.start.character
            && self.as_lsp().start.line == other.start.line
            && self.as_lsp().end.character == other.end.character
            && self.as_lsp().end.line == other.end.line
    }
}

impl PartialOrd<lsp_types::Range> for RangeKind<'_> {
    fn partial_cmp(&self, other: &lsp_types::Range) -> Option<std::cmp::Ordering> {
        Some(self.cmp(&other.into()))
    }
}

#[bon::builder]
pub fn diag<'a>(
    range: RangeKind<'a>,
    message: String,
    source: Option<String>,
    severity: Option<DiagnosticSeverity>,
    tags: Option<Vec<DiagnosticTag>>,
    code_description: Option<lsp_types::CodeDescription>,
    code: Option<NumberOrString>,
    related_information: Option<Vec<DiagnosticRelatedInformation>>,
    fixes: Option<Vec<CodeAction>>,
) -> IdeDiagnostic {
    IdeDiagnostic {
        diagnostic: auto_lsp::lsp_types::Diagnostic {
            range: range.as_lsp(),
            severity,
            source,
            message,
            code,
            code_description,
            related_information,
            tags,
            data: None,
        },
        fixes: fixes.unwrap_or_default(),
    }
}

#[bon::builder]
pub fn action(
    title: String,
    kind: Option<CodeActionKind>,
    diagnostics: Option<Vec<auto_lsp::lsp_types::Diagnostic>>,
    is_preferred: Option<bool>,
    edit: Option<auto_lsp::lsp_types::WorkspaceEdit>,
    command: Option<auto_lsp::lsp_types::Command>,
    disabled: Option<auto_lsp::lsp_types::CodeActionDisabled>,
) -> CodeAction {
    CodeAction {
        title,
        kind,
        diagnostics,
        is_preferred,
        edit,
        command,
        data: None,
        disabled,
    }
}

#[bon::builder]
pub fn edit<'a>(range: RangeKind<'a>, new_text: String) -> TextEdit {
    TextEdit::new(
        range.as_lsp(),
        new_text,
    )
}