use std::collections::HashMap;

use auto_lsp::core::errors::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ParseErrorAccumulator};
use auto_lsp::lsp_types::{DiagnosticRelatedInformation, WorkspaceEdit};

use crate::diagnostics::diagnostic_builder::{action, diag, edit};
use crate::diagnostics::IdeDiagnostic;

pub fn lexer_error_with_fix(
    db: &dyn auto_lsp::default::db::BaseDatabase,
    file: &auto_lsp::default::db::File,
    errors: &mut Vec<&ParseErrorAccumulator>,
) -> Vec<IdeDiagnostic> {
    errors
        .iter()
        .map(|error| process_parse_error(db, file, error))
        .collect()
}

fn process_parse_error(
    db: &dyn auto_lsp::default::db::BaseDatabase,
    file: &auto_lsp::default::db::File,
    error: &ParseErrorAccumulator,
) -> IdeDiagnostic {
    match (*error).into() {
        ParseError::LexerError { range, error: lexer_error } => {
            process_lexer_error(db, file, range, lexer_error)
        }
        _ => (*error).into(),
    }
}

fn process_lexer_error(
    db: &dyn auto_lsp::default::db::BaseDatabase,
    file: &auto_lsp::default::db::File,
    range: auto_lsp::tree_sitter::Range,
    lexer_error: LexerError,
) -> IdeDiagnostic {
    match lexer_error {
        LexerError::Missing { error: missing_error, grammar_name, .. } => {
            create_missing_diagnostic(db, file, range, missing_error, grammar_name)
        }
        LexerError::Syntax { range: affected_range, error: syntax_error, affected } => {
            create_syntax_diagnostic(db, file, affected_range, syntax_error, affected)
        }
        _ => create_generic_lexer_diagnostic(range),
    }
}

fn create_missing_diagnostic(
    db: &dyn auto_lsp::default::db::BaseDatabase,
    file: &auto_lsp::default::db::File,
    range: auto_lsp::tree_sitter::Range,
    missing_error: String,
    grammar_name: String,
) -> IdeDiagnostic {
    let mut diagnostic = diag()
        .range(range.into())
        .message(missing_error)
        .source("IEC".into())
        .severity(auto_lsp::lsp_types::DiagnosticSeverity::ERROR)
        .related_information(vec![DiagnosticRelatedInformation {
            location: auto_lsp::lsp_types::Location {
                uri: file.url(db).clone(),
                range: range.into(),
            },
            message: format!("help: add missing {grammar_name} here"),
        }])
        .call();

    let fix = action()
        .title(format!("Insert missing '{grammar_name}'"))
        .kind(auto_lsp::lsp_types::CodeActionKind::QUICKFIX)
        .diagnostics(vec![diagnostic.diagnostic.clone()])
        .is_preferred(true)
        .edit(create_workspace_edit(
            db,
            file,
            range,
            grammar_name,
        ))
        .call();

    diagnostic.with_fix(fix);
    diagnostic
}

fn create_syntax_diagnostic(
    db: &dyn auto_lsp::default::db::BaseDatabase,
    file: &auto_lsp::default::db::File,
    affected_range: auto_lsp::tree_sitter::Range,
    syntax_error: String,
    affected: Vec<String>,
) -> IdeDiagnostic {
    if affected.len() != 1 {
        return create_generic_syntax_diagnostic(affected_range, syntax_error);
    }

    let affected_text = &affected[0];
    let mut diagnostic = diag()
        .range(affected_range.into())
        .message(syntax_error)
        .source("IEC".into())
        .severity(auto_lsp::lsp_types::DiagnosticSeverity::ERROR)
        .related_information(vec![DiagnosticRelatedInformation {
            location: auto_lsp::lsp_types::Location {
                uri: file.url(db).clone(),
                range: affected_range.into(),
            },
            message: format!("help: remove '{affected_text}'"),
        }])
        .call();

    let fix = action()
        .title(format!("Remove {affected_text}"))
        .kind(auto_lsp::lsp_types::CodeActionKind::QUICKFIX)
        .diagnostics(vec![diagnostic.diagnostic.clone()])
        .is_preferred(true)
        .edit(create_workspace_edit(
            db,
            file,
            affected_range,
            String::new(), // Empty string to remove the text
        ))
        .call();

    diagnostic.with_fix(fix);
    diagnostic
}

fn create_workspace_edit(
    db: &dyn auto_lsp::default::db::BaseDatabase,
    file: &auto_lsp::default::db::File,
    range: auto_lsp::tree_sitter::Range,
    new_text: String,
) -> WorkspaceEdit {
    WorkspaceEdit::new(HashMap::from([(
        file.url(db).clone(),
        vec![edit()
            .new_text(new_text)
            .range(range.into())
            .call()],
    )]))
}

fn create_generic_lexer_diagnostic(range: auto_lsp::tree_sitter::Range) -> IdeDiagnostic {
    diag()
        .range(range.into())
        .message("Lexer error")
        .source("IEC".into())
        .severity(auto_lsp::lsp_types::DiagnosticSeverity::ERROR)
        .call()
}

fn create_generic_syntax_diagnostic(
    range: auto_lsp::tree_sitter::Range,
    syntax_error: String,
) -> IdeDiagnostic {
    diag()
        .range(range.into())
        .message(syntax_error)
        .source("IEC".into())
        .severity(auto_lsp::lsp_types::DiagnosticSeverity::ERROR)
        .call()
}
