use std::collections::HashMap;

use auto_lsp::core::errors::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ParseErrorAccumulator};
use auto_lsp::lsp_types::{DiagnosticRelatedInformation, TextEdit, WorkspaceEdit,
};

use crate::diagnostics::diagnostic_builder::{action, diag, edit};
use crate::diagnostics::IdeDiagnostic;

pub fn lexer_error_with_fix(
    db: &dyn auto_lsp::default::db::BaseDatabase,
    file: &auto_lsp::default::db::File,
    errors: &mut Vec<&ParseErrorAccumulator>,
) -> Vec<IdeDiagnostic> {
    errors
        .into_iter()
        .filter_map(|error| {
            if let ParseError::LexerError {
                range,
                error: lexer_error,
            } = (*error).into()
            {
                if let LexerError::Missing {
                    error: missing_error,
                    grammar_name,
                    ..
                } = lexer_error
                {
                    let mut diagnostic = diag()
                        .range(range.into())
                        .message(missing_error.to_string())
                        .source("IEC".into())
                        .severity(auto_lsp::lsp_types::DiagnosticSeverity::ERROR)
                        .related_information(vec![DiagnosticRelatedInformation {
                            location: auto_lsp::lsp_types::Location {
                                uri: file.url(db).clone(),
                                range: range.into(),
                            },
                            message: format!("help: add missing {grammar_name} here").into(),
                        }])
                        .call();

                    diagnostic.with_fix(
                        action()
                            .title(format!("Insert missing '{}'", grammar_name))
                            .kind(auto_lsp::lsp_types::CodeActionKind::QUICKFIX)
                            .diagnostics(vec![diagnostic.diagnostic.clone()])
                            .is_preferred(true)
                            .edit(WorkspaceEdit::new(HashMap::from([(
                                file.url(db).clone(),
                                vec![edit()
                                    .new_text(grammar_name.to_string())
                                    .range(range.into())
                                    .call()],
                            )])))
                            .call(),
                    );

                    return Some(diagnostic);
                } else if let LexerError::Syntax {
                    range: affected_range,
                    error: syntax_error,
                    affected,
                } = lexer_error
                {
                    if affected.len() == 1 {

                        let mut diagnostic = diag()
                            .range(affected_range.into())
                            .message(syntax_error.to_string())
                            .source("IEC".into())
                            .severity(auto_lsp::lsp_types::DiagnosticSeverity::ERROR)
                            .related_information(vec![DiagnosticRelatedInformation {
                                location: auto_lsp::lsp_types::Location {
                                    uri: file.url(db).clone(),
                                    range: affected_range.into(),
                                },
                                message: format!("help: remove '{affected}'").into(),
                            }])
                            .call();

                        diagnostic.with_fix(
                            action()
                                .title(format!("Remove {affected}"))
                                .kind(auto_lsp::lsp_types::CodeActionKind::QUICKFIX)
                                .diagnostics(vec![diagnostic.diagnostic.clone()])
                                .is_preferred(true)
                                .edit(WorkspaceEdit::new(HashMap::from([(
                                    file.url(db).clone(),
                                    vec![edit()
                                        .new_text("".to_string())
                                        .range(affected_range.into())
                                        .call()],
                                )])))
                                .call(),
                        );
                        return Some(diagnostic);
                    } else {
                        // Handle other LexerError variants if needed
                        return Some((*error).into());
                    }
                } else {
                    // Handle other ParseError variants if needed
                    return Some((*error).into());
                }
            }
            return Some((*error).into());
        })
        .collect()
}
