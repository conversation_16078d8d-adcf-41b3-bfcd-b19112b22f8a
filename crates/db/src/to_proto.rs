use auto_lsp::lsp_types::{self, SymbolKind};

use crate::diagnostics::diagnostic_builder::RangeKind;

#[derive(bon::<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Debug)]
pub struct SymbolInfo<'a> {
    pub range: RangeKind<'a>,
    pub name: String,
    pub name_range: RangeKind<'a>,
    pub kind: Option<SymbolKind>,
}

pub trait ToProto<'db> {
    fn symbol_info(&'db self, db: &'db dyn crate::BaseDatabase) -> SymbolInfo<'db>;
}

pub trait IterToProto<'db> {
    fn iter(&'db self, db: &'db dyn crate::BaseDatabase) -> impl Iterator<Item = SymbolInfo<'db>>;

    fn descendant_at(&'db self, db: &'db dyn crate::BaseDatabase, range: &lsp_types::Position) -> Option<SymbolInfo<'db>> {
        self.iter(db)
            .filter(|symbol| {
                let symbol_range = symbol.range.as_lsp();
                symbol_range.start <= *range && *range <= symbol_range.end
            })
            .min_by_key(|symbol| {
                let symbol_range = symbol.range.as_lsp();
                // Prefer smaller ranges (more specific symbols)
                let size = (symbol_range.end.line - symbol_range.start.line, symbol_range.end.character - symbol_range.start.character);
                // Prefer symbols that start later (they're "inside" the range)
                let start = symbol_range.start;
                (size, start)
            })
    }
}