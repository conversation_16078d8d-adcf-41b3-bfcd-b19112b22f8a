use auto_lsp::lsp_types::SymbolKind;

use crate::diagnostics::diagnostic_builder::RangeKind;

#[derive(bon::<PERSON>uild<PERSON>, <PERSON><PERSON>, Debug)]
pub struct SymbolInfo<'a> {
    pub range: RangeKind<'a>,
    pub name: String,
    pub name_range: RangeKind<'a>,
    pub kind: Option<SymbolKind>,
}

pub trait ToProto<'db> {
    fn symbol_info(&'db self, db: &'db dyn crate::BaseDatabase) -> SymbolInfo<'db>;
}

pub trait IterToProto<'db> {
    fn iter(&'db self, db: &'db dyn crate::BaseDatabase) -> impl Iterator<Item = SymbolInfo<'db>>;
}