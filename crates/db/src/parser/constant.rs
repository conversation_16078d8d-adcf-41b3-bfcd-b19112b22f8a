use std::ops::Deref;

use auto_lsp::default::db::{BaseDatabase, File};

use crate::{hir::variable::{BitString, Literal}, ident::Ident, RootDatabase};

trait ParseConstant<'db> {
    fn parse(&self, db: &'db dyn BaseDatabase, file: File) -> Literal;
}

impl<'db> ParseConstant<'db> for ast::generated::Constant {

    fn parse(&self, db: &'db dyn BaseDatabase, file: File) -> Literal {
        type Constant = ast::generated::BitStrLiteral_BoolLiteral_CharLiteral_NumericLiteral_TimeLiteral;

        match self.children.deref() {
            Constant::BoolLiteral(bool_literal) => {
                Literal::Bool(Ident::from_node(db, file, bool_literal.value.deref()).unwrap())
            },
            Constant::BitStrLiteral(bit_str_literal) => {
                match bit_str_literal.int.deref() {
                    ast::generated::BinaryInt_HexInt_OctalInt_UnsignedInt::BinaryInt(binary_int) => {
                        let bits = binary_int.children
                            .iter()
                            .map(|bit| Ident::from_node(db, file, bit.deref()).unwrap())
                            .collect::<Vec<_>>();
                        Literal::BitString(BitString::Binary(Ident::join(db, &bits)))
                    },
                    ast::generated::BinaryInt_HexInt_OctalInt_UnsignedInt::HexInt(hex_int) => {
                        let hex = hex_int.children
                            .iter()
                            .map(|hex| Ident::from_node(db, file, hex.deref()).unwrap())
                            .collect::<Vec<_>>();
                        Literal::BitString(BitString::Hex(Ident::join(db, &hex)))
                    },
                    ast::generated::BinaryInt_HexInt_OctalInt_UnsignedInt::OctalInt(octal_int) => {
                        let octal = octal_int.children
                            .iter()
                            .map(|octal| Ident::from_node(db, file, octal.deref()).unwrap())
                            .collect::<Vec<_>>();
                        Literal::BitString(BitString::Octal(Ident::join(db, &octal)))
                    },
                    ast::generated::BinaryInt_HexInt_OctalInt_UnsignedInt::UnsignedInt(unsigned_int) => {
                        let unsigned = unsigned_int.children
                            .iter()
                            .map(|unsigned| Ident::from_node(db, file, unsigned.deref()).unwrap())
                            .collect::<Vec<_>>();
                        Literal::BitString(BitString::Unsigned(Ident::join(db, &unsigned)))
                    },
                }

            },
            Constant::CharLiteral(char_literal) => {
                match char_literal.char.children.deref() {
                    ast::generated::DByteCharStr_SByteCharStr::SByteCharStr(s_byte_char_str) => {
                        let char = Ident::from_node(db, file, s_byte_char_str.char.deref()).unwrap();
                        Literal::Char(char)
                    },
                    ast::generated::DByteCharStr_SByteCharStr::DByteCharStr(d_byte_char_str) => {
                        let char = Ident::from_node(db, file, d_byte_char_str.char.deref()).unwrap();
                        Literal::Char(char)
                    },
                }
            },
            Constant::NumericLiteral(numeric_literal) => {
                match numeric_literal.children.deref() {
                    ast::generated::IntLiteral_RealLiteral::IntLiteral(int_literal) => {
                        let int = Ident::from_node(db, file, int_literal.int.deref()).unwrap();
                        Literal::Numeric(int)
                    },
                    ast::generated::IntLiteral_RealLiteral::RealLiteral(real_literal) => {
                        let real = Ident::from_node(db, file, real_literal.real.deref()).unwrap();
                        Literal::Numeric(real)
                    },
                }
            },
            Constant::TimeLiteral(time_literal) => {
                todo!()
                /*match time_literal.children.deref() {
                    ast::generated::Date_DateAndTime_Duration_TimeOfDay::Date(date) => {
                        let date = Ident::from_node(db, file, &date.children).unwrap();
                        Literal::Time(date)
                    },
                    ast::generated::Date_DateAndTime_Duration_TimeOfDay::DateAndTime(date_and_time) => {
                        let date = Ident::from_node(db, file, date_and_time.date_literal.deref()).unwrap();
                        let time = Ident::from_node(db, file, date_and_time.daytime.deref()).unwrap();
                        Literal::Time(Ident::join(db, &[date, time]))
                    },
                    ast::generated::Date_DateAndTime_Duration_TimeOfDay::Duration(duration) => {
                        let duration = Ident::from_node(db, file, duration.interval.deref()).unwrap();
                        Literal::Time(duration)
                    },
                    ast::generated::Date_DateAndTime_Duration_TimeOfDay::TimeOfDay(time_of_day) => {
                        let time = Ident::from_node(db, file, time_of_day.daytime.deref()).unwrap();
                        Literal::Time(time)
                    },
                }*/
            },
        }
    }
}
