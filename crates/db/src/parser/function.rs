use std::ops::Deref;

use crate::hir;
use crate::hir::variable::{Array, BitString, Literal, PrimitiveKind, Variable, VariableKind};
use crate::parser::Parse;
use crate::ident::Ident;
use ast::generated::FuncVariables;
use auto_lsp::core::ast::AstNode;
use auto_lsp::default::db::{BaseDatabase, File};

impl<'db> Parse<'db> for ast::generated::FuncDecl {
    type Output = hir::function::Function<'db>;

    fn parse(&self, db: &'db dyn BaseDatabase, file: File) -> Self::Output {
        let input_variables = self.parse_variables(db, file);
        hir::function::Function::new(db, input_variables, vec![], vec![], vec![], vec![], vec![])
    }
}

trait ParseVariable<'db> {
    fn parse_variables(&self, db: &'db dyn BaseDatabase, file: File) -> Vec<Variable<'db>>;
}

trait ToVariable<'db> {
    fn to_variables(&self, db: &'db dyn BaseDatabase, file: File) -> Variable<'db>;
}

impl<'db> ToVariable<'db> for ast::generated::VarDeclInit {
    fn to_variables(&self, db: &'db dyn BaseDatabase, file: File) -> Variable<'db> {
        match self.init.deref() {
            ast::generated::VarDeclKind::ArraySpecInit(array) => todo!(),
            ast::generated::VarDeclKind::StrVarDecl(_) => todo!(),
            ast::generated::VarDeclKind::SimpleVarDecl(_) => todo!(),
        }
    }
}

impl<'db> ParseVariable<'db> for ast::generated::FuncDecl {
    fn parse_variables(&self, db: &'db dyn BaseDatabase, file: File) -> Vec<Variable<'db>> {
        let mut intput_variables = vec![];
        self.variables
            .iter()
            .for_each(|variable| match variable.deref() {
                FuncVariables::InputDecls(variable) => {
                    variable
                        .children
                        .iter()
                        .for_each(|variable| match variable.deref() {
                            ast::generated::InputVarKind::ArrayConformDecl(array_conform_decl) => {
                                array_conform_decl.variables.children.iter().for_each(|variable| {
                                    intput_variables.push(Variable::from(
                                        db,
                                        variable.get_text(file.document(db).as_bytes()).unwrap(),
                                        VariableKind::Array(todo!()),
                                    ));
                                });
                            }
                            ast::generated::InputVarKind::EdgeDecl(edge_decl) => {
                                edge_decl.variables.children.iter().for_each(|variable| {
                                    intput_variables.push(Variable::from(
                                        db,
                                        variable.get_text(file.document(db).as_bytes()).unwrap(),
                                        VariableKind::Edge
                                    ));
                                });
                            }
                            ast::generated::InputVarKind::VarDeclInit(var_decl_init) => {
                                let kind = match var_decl_init.init.deref() {
                                    ast::generated::VarDeclKind::ArraySpecInit(_) => VariableKind::Array(todo!()),
                                    ast::generated::VarDeclKind::StrVarDecl(_) => VariableKind::Primitive(PrimitiveKind::String),
                                    ast::generated::VarDeclKind::SimpleVarDecl(_) => {
                                        todo!()
                                    }
                                };
                                
                                
                                var_decl_init
                                    .variables
                                    .children
                                    .iter()
                                    .for_each(|variable| {
                                        intput_variables.push(Variable::from(
                                            db,
                                            variable.get_text(file.document(db).as_bytes()).unwrap(),
                                            VariableKind::Primitive(PrimitiveKind::Bool),
                                        ));
                                    });
                            }
                        });
                }
                FuncVariables::OutputDecls(variable) => {}
                FuncVariables::InOutDecls(variable) => {}
                FuncVariables::ExternalVarDecls(variable) => {}
                FuncVariables::TempVarDecls(variable) => {}
                FuncVariables::VarDecls(variable) => {}
            });

        intput_variables
    }
}
