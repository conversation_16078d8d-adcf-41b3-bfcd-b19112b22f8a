
use crate::{hir::variable::Variable, to_proto::{IterToProto, SymbolInfo, SymbolInfoBuilder, ToProto}};

#[salsa::tracked(debug)]
pub struct Function<'db> {
    #[returns(ref)]
    pub input_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub output_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub in_out_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub temp_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub external_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub global_variables: Vec<Variable<'db>>,
}
