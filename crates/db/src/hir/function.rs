
use crate::{hir::variable::Variable, to_proto::{IterToProto, SymbolInfo, SymbolInfoBuilder, ToProto}};

#[salsa::tracked(debug)]
pub struct Function<'db> {
    #[returns(ref)]
    pub input_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub output_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub in_out_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub temp_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub external_variables: Vec<Variable<'db>>,
    #[returns(ref)]
    pub global_variables: Vec<Variable<'db>>,
}

impl<'db> ToProto<'db> for Variable<'db> {
    fn symbol_info(&self, db: &dyn crate::BaseDatabase) -> SymbolInfo {
        SymbolInfo::builder().build()
    }
}

impl<'db> IterToProto<'db> for Function<'db> {
    /// Returns an iterator over all variables converted into `SymbolInfo`s.
    fn iter(&'db self, db: &'db dyn crate::BaseDatabase)
        -> impl Iterator<Item = SymbolInfo>
    {
        self.input_variables(db)
            .iter()
            .chain(self.output_variables(db).iter())
            .chain(self.in_out_variables(db).iter())
            .chain(self.temp_variables(db).iter())
            .chain(self.external_variables(db).iter())
            .chain(self.global_variables(db).iter())
            .map(|v| v.symbol_info(db))
    }
}
