[package]
name = "tree-sitter-iec-61131-3"
description = "Iec611313 grammar for tree-sitter"
version = "0.1.0"
authors = ["CLAUZEL Adrien <<EMAIL>>"]
license = "GPL-3.0"
readme = "README.md"
keywords = ["incremental", "parsing", "tree-sitter", "iec-61131-3"]
categories = ["parser-implementations", "parsing", "text-editors"]
repository = "https://github.com/adclz/iec-grammar"
edition = "2021"
autoexamples = false

build = "bindings/rust/build.rs"
include = [
  "bindings/rust/*",
  "grammar.js",
  "queries/*",
  "src/*",
  "tree-sitter.json",
  "LICENSE",
]

[lib]
path = "bindings/rust/lib.rs"

[dependencies]
tree-sitter-language = "0.1"

[build-dependencies]
cc = "1.2"

[dev-dependencies]
tree-sitter = "0.25.4"
