[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tree-sitter-iec-61131-3"
description = "Iec611313 grammar for tree-sitter"
version = "0.1.0"
keywords = ["incremental", "parsing", "tree-sitter", "iec-61131-3"]
classifiers = [
  "Intended Audience :: Developers",
  "Topic :: Software Development :: Compilers",
  "Topic :: Text Processing :: Linguistic",
  "Typing :: Typed",
]
authors = [{ name = "CLAUZEL Adrien", email = "<EMAIL>" }]
requires-python = ">=3.10"
license.text = "GPL-3.0"
readme = "README.md"

[project.urls]
Homepage = "https://github.com/adclz/iec-grammar"

[project.optional-dependencies]
core = ["tree-sitter~=0.24"]

[tool.cibuildwheel]
build = "cp310-*"
build-frontend = "build"
