// swift-tools-version:5.3

import Foundation
import PackageDescription

var sources = ["src/parser.c"]
if FileManager.default.fileExists(atPath: "src/scanner.c") {
    sources.append("src/scanner.c")
}

let package = Package(
    name: "TreeSitterIec611313",
    products: [
        .library(name: "TreeSitterIec611313", targets: ["TreeSitterIec611313"]),
    ],
    dependencies: [
        .package(url: "https://github.com/tree-sitter/swift-tree-sitter", from: "0.8.0"),
    ],
    targets: [
        .target(
            name: "TreeSitterIec611313",
            dependencies: [],
            path: ".",
            sources: sources,
            resources: [
                .copy("queries")
            ],
            publicHeadersPath: "bindings/swift",
            cSettings: [.headerSearchPath("src")]
        ),
        .testTarget(
            name: "TreeSitterIec611313Tests",
            dependencies: [
                "SwiftTreeSitter",
                "TreeSitterIec611313",
            ],
            path: "bindings/swift/TreeSitterIec611313Tests"
        )
    ],
    cLanguageStandard: .c11
)
