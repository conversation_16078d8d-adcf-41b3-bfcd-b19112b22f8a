{"$schema": "https://tree-sitter.github.io/tree-sitter/assets/schemas/config.schema.json", "grammars": [{"name": "iec_61131_3", "camelcase": "IEC_61131_3", "title": "IEC_61131_3", "scope": "source.iec_61131_3", "file-types": [".st"], "injection-regex": "^iec_61131_3$", "class-name": "TreeSitterIec611313"}], "metadata": {"version": "0.1.0", "license": "GPL-3.0", "description": "Iec611313 grammar for tree-sitter", "authors": [{"name": "CLAUZEL Adrien", "email": "clauzelad<PERSON>@mail.com"}], "links": {"repository": "https://github.com/adclz/iec-grammar"}}, "bindings": {"c": true, "go": true, "node": true, "python": true, "rust": true, "swift": true, "zig": false}}