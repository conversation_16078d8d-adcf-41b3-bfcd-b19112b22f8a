{"name": "tree-sitter-iec-61131-3", "version": "0.1.0", "description": "Iec611313 grammar for tree-sitter", "repository": "https://github.com/adclz/iec-grammar", "license": "GPL-3.0", "author": {"name": "CLAUZEL Adrien", "email": "clauzelad<PERSON>@mail.com"}, "main": "bindings/node", "types": "bindings/node", "keywords": ["incremental", "parsing", "tree-sitter", "iec_61131_3"], "files": ["grammar.js", "tree-sitter.json", "binding.gyp", "prebuilds/**", "bindings/node/*", "queries/*", "src/**", "*.wasm"], "dependencies": {"node-addon-api": "^8.2.1", "node-gyp-build": "^4.8.2"}, "devDependencies": {"prebuildify": "^6.0.1", "tree-sitter-cli": "^0.25.4"}, "peerDependencies": {"tree-sitter": "^0.21.1"}, "peerDependenciesMeta": {"tree-sitter": {"optional": true}}, "scripts": {"install": "node-gyp-build", "prestart": "tree-sitter build --wasm", "start": "tree-sitter playground", "test": "node --test bindings/node/*_test.js"}}