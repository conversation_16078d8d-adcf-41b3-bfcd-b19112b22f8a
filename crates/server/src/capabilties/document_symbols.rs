#![allow(deprecated)]


use ast::generated::{ClassDecl, ClassDecl_DataTypeDecl_FbDecl_FuncDecl_InterfaceDecl_NamespaceDecl, ConfigDecl_NamespaceDecl_ProgDecl, FbDecl, FuncDecl, InterfaceDecl, NamespaceDecl, SourceFile};
use auto_lsp::{
    anyhow, core::{
        ast::AstNode,
        dispatch,
        document::Document,
        document_symbols_builder::DocumentSymbolsBuilder,
    }, default::db::{tracked::get_ast, BaseDatabase}, lsp_types::{DocumentSymbol, DocumentSymbolParams, DocumentSymbolResponse, SymbolKind}
};
use db::{solver::namespaces_in_file, to_proto::IterToProto};

pub fn document_symbols(
    db: &impl BaseDatabase,
    params: DocumentSymbolParams,
) -> anyhow::Result<Option<DocumentSymbolResponse>> {
    let uri = params.text_document.uri;

    let file = db
        .get_file(&uri)
        .ok_or_else(|| anyhow::format_err!("File not found in workspace"))?;

    let mut builder = DocumentSymbolsBuilder::default();
    let ns = namespaces_in_file(db, file).unwrap();
    
    ns.iter(db).for_each(|symbol| {
        if let Some(kind) = symbol.kind {
            builder.push_symbol(DocumentSymbol {
                name: symbol.name,
                detail: None,
                kind,
                selection_range: symbol.name_range.into(),
                deprecated: None,
                tags: None,
                children: None,
                range: symbol.range.into(),
            });
            return;
        }
    });
    Ok(Some(DocumentSymbolResponse::Nested(builder.finalize())))
}
