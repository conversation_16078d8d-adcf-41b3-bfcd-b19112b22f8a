[package]
name = "ast"
authors = ["<PERSON><PERSON><PERSON>"]
license = "GPL-3.0"
repository = "https://github.com/adclz/auto-lsp"
version = "0.1.0"
edition = "2021"
publish = false

[dependencies]
auto-lsp = { workspace = true }
tree-sitter-iec-61131-3 = { path = "../tree-sitter" }

[dev-dependencies]
insta = { version = "1.43.1", features = ["filters"] }
rstest = "0.25.0"


[profile.dev.package]
insta.opt-level = 3
similar.opt-level = 3

[build-dependencies]
auto-lsp-codegen = { workspace = true }
auto-lsp = { workspace = true }
tree-sitter-iec-61131-3 = { path = "../tree-sitter" }
