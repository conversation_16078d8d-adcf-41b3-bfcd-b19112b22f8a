{"$schema": "https://raw.githubusercontent.com/martinring/tmlanguage/master/tmlanguage.json", "name": "Structured Text", "patterns": [{"include": "#comments"}], "repository": {"comments": {"patterns": [{"include": "#comment_l"}, {"include": "#comment_st"}, {"include": "#comment_g"}], "repository": {"comment_l": {"name": "comment.line.double-slash.st", "begin": "\\/\\/", "end": "$", "patterns": [{"name": "constant.character.escape.st", "match": "\\\\."}]}, "comment_st": {"name": "comment.block.st", "begin": "\\(\\*", "end": "\\*\\)", "patterns": [{"include": "#comment_st"}, {"match": "(@[a-zA-Z_]*)\\s*(:=)\\s+([\"\\']{1}[^\"\\']*[\"\\']{1})", "captures": {"1": {"name": "variable.parameter.st"}, "2": {"name": "keyword.operator.assignment.st"}, "3": {"name": "string.quoted.single.st"}}}]}, "comment_g": {"name": "comment.block.st", "begin": "\\/\\*", "end": "\\*\\/", "patterns": [{"include": "#comment_g"}, {"_name": "constant.character.escape.st", "_match": "\\\\."}]}}}}, "scopeName": "source.st"}