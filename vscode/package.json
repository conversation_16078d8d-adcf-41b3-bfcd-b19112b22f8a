{"name": "vscode-wasi", "description": ".", "author": "<PERSON><PERSON>", "license": "MIT", "version": "1.0.0", "publisher": "adclz", "categories": [], "keywords": [], "engines": {"vscode": "^1.88.0"}, "main": "./client/out/extension", "browser": "./client/dist/web/extension", "activationEvents": ["*"], "contributes": {"languages": [{"id": "st", "aliases": ["structured_text", "St", "st"], "extensions": [".st"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "st", "scopeName": "source.st", "path": "./syntaxes/comment.tmLanguage.json"}], "semanticTokenScopes": [{"language": "st", "scopes": {"namespace": ["entity.name.namespace.st"], "function": ["entity.name.function.st"], "class": ["entity.name.class.st"], "interface": ["entity.name.type.interface.st"], "keyword": ["keyword.other.st"], "keyword.control": ["keyword.control.st"], "modifier": ["markup.underline"], "*.oop": ["keyword.control.st"]}}]}, "dependencies": {}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^7.14.0", "@typescript-eslint/parser": "^7.14.0", "@types/node": "^20", "eslint": "^8.57.0", "typescript": "^5.6.2", "esbuild": "^0.21.3", "serve": "^14.2.3"}, "scripts": {"postinstall": "cd client && npm install && cd ..", "vscode:prepublish": "npm run build", "build": "cd client && npm run compile && cd ../server && npm run build && cd ..", "lint": "cd client && npm run lint && cd ..", "esbuild": "node ./bin/esbuild.js"}}