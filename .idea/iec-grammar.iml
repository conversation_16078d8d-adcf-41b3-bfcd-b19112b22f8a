<?xml version="1.0" encoding="UTF-8"?>
<module type="EMPTY_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/crates/ast/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/crates/db/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/crates/server/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/crates/tree-sitter/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/vscode/server/src" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/crates/tree-sitter/target" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>