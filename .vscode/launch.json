{
	// Use IntelliSense to learn about possible attributes.
	// Hover to view descriptions of existing attributes.
	// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
		{
			"type": "extensionHost",
			"request": "launch",
			"name": "Run Vscode LSP",
			"runtimeExecutable": "${execPath}/vscode",
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/vscode",
				"${workspaceFolder}/vscode/test"
			],
			"outFiles": [
				"${workspaceFolder}/vscode/client/out/**/*.js"
			],
			"autoAttachChildProcesses": true,
			"preLaunchTask": "npm: build native"
		}
	]
}